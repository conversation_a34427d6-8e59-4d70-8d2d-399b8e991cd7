# 大纲（Markdown）

1. 领域模型与对象关系
   1.1 核心实体：`ProcessNode`、`BaseNode` 及节点能力接口（`IRoutable`、`IHasBranches`、`IHasConditions`、`IHasSubProcess`）
   1.2 网关类型与约束：并行分支 / 唯一分支
   1.3 “结束节点”哨兵语义：`nextId=99`、`nextId=""`
   1.4 “编辑上下文”与“游标（Cursor）”模型

2. 遍历器与访问器
   2.1 非 DAG 前提下的深度优先遍历（DFS）与广度优先遍历（BFS）选择
   2.2 `Visitor` 模式：统一对网关、分支、普通节点、子流程的进入/退出回调
   2.3 子流程的递归遍历与断环防护

3. 通用“上下文自动连线”策略（贯穿所有 CRUD）
   3.1 `Strategy` 模式：断开、连接、替换、插入、抽出
   3.2 `Policy` 组合：网关前后上下文、分支尾部、子流程入口/出口
   3.3 不同场景的连线优先级与冲突消解

4. 网关操作
   4.1 新增网关（默认并行），含“左侧整体放置 / 不移动”两种落位策略
   4.2 删除网关（含“仅剩一条分支”自动坍缩为普通链路）
   4.3 修改网关类型（并行 ↔ 唯一）

5. 分支操作
   5.1 增加分支
   5.2 删除分支
   5.3 调整分支顺序
   5.4 复制分支

6. 普通节点操作
   6.1 新增（插入到某上下文）
   6.2 删除（自动连线）
   6.3 修改（就地替换/属性编辑）

7. 约束校验与健壮性
   7.1 结束节点唯一性校验（主流程 / 子流程各自唯一）
   7.2 路由合法性（`IRoutable.hasValidRoute()`）
   7.3 分支合法性（`IHasBranches.hasValidBranches()`）
   7.4 条件合法性（`IHasConditions.hasValidConditions()`）
   7.5 子流程合法性（`IHasSubProcess.hasValidSubProcess()`）
   7.6 整体一致性扫描（前驱/后继、游离节点、悬空分支）

8. 事务化编辑与冲突处理
   8.1 `Unit of Work` 聚合一次变更
   8.2 失败即回退（不实现撤销/重做）
   8.3 并发编辑的乐观锁思路（可选）

9. 工具与基础设施
   9.1 `Factory`/`Builder`：节点与网关构造
   9.2 `Repository`：流程装载/持久化/快照
   9.3 ID 生成器与命名器，审计日志

10. 基于当前 `workFlow.txt` 的示例走查
    10.1 识别网关与分支（`gatewayType=2`、`flowIds`）
    10.2 `nextId=99` 与 `nextId=""` 的实际落点
    10.3 子流程的入口/出口示例

---

# 可落地实现方案

## 1. 领域模型与对象关系

### 1.1 节点与能力接口（基于现有接口）

* **`IRoutable`**：提供 `getNextId()/setNextId()` 与默认校验 `hasValidRoute()`（允许空/“99”语义）。
* **`IHasBranches`**：提供 `getFlowIds()`，并带有安全添加/删除的默认方法和 `hasValidBranches()` 校验。
* **`IHasConditions`**：`getOperateCondition()` 与 `hasValidConditions()`，保证每个条件组元素具备必要标识。
* **`IHasSubProcess`**：暴露 `getProcessNode()`，并提供 `hasValidSubProcess()` 校验。
* **`ProcessNode`**：流程根，含 `id`、`startEventId`、`flowNodeMap<id, BaseNode>`、`child`、`execIds`、`execPendingIds`，以及 `toJson()`（Jackson 序列化）。这些字段在当前文件中可见（例如 `startEventId`、`flowNodeMap`、`child` 与 `toJson()`），与样例结构一致。

> **模式**：用 *接口能力组合*（Capability）替代继承层级膨胀；节点仅在具备能力时实现相应接口。

### 1.2 网关类型与约束

* 定义枚举 `GatewayType { PARALLEL, EXCLUSIVE }`，分别对应 **并行分支** / **唯一分支**（样例中 `gatewayType=2` 表示唯一分支）。
* 网关节点具备 `IHasBranches`，其 `flowIds` 为**分支头节点 ID 列表**。

### 1.3 结束节点哨兵语义

* **`nextId=99`**：连接至结束节点（主流程和每个子流程**各自最多一个**）。样例中既有主流程普通节点 `nextId=99`，也有子流程节点 `nextId=99`。
* **`nextId=""`**：分支尾部无后继（等待汇集）。样例中可见分支尾部节点有空 `nextId`。

### 1.4 “编辑上下文”与“游标（Cursor）”

* **`EditContext`**（值对象）：

    * `processId`、`nodeId`、`prevId`（可选）
    * `scope`：`MAIN` | `SUB(processNodeId)`
    * `position`：`BEFORE` | `AFTER` | `INSIDE_BRANCH(branchIndex)` | `AT_GATEWAY`
* **用法**：所有 CRUD 均以 `EditContext` 指定操作落位，避免模糊定位导致的副作用。

---

## 2. 遍历器与访问器

### 2.1 遍历器（`GraphTraverser`）

* **输入**：`ProcessNode` + 回调（Visitor）。
* **策略**：优先使用 *DFS*（链式结构友好），必要时提供 BFS。
* **起点**：`startEventId`。
* **分支**：遇到网关，依次遍历其 `flowIds` 指向的分支头。
* **子流程**：遇到 `IHasSubProcess` 的节点，**递归进入其 `processNode`**（样例中外层节点含 `processNode` 且内部再有 `nextId=99` 的审批节点）。

### 2.2 访问器（`NodeVisitor`，*Visitor* 模式）

* 针对四类对象（网关、分支、普通节点、子流程）分别定义 `enter/leave` 回调；
* 遍历时统一触发，便于统计、校验、重接线等横切逻辑。

### 2.3 断环与越界防护

* **已访问集合**：`visitedIds`（每个流程作用域独立）；
* **最大深度/步数**阈值；
* **孤立 ID** 提示：在 *Repository* 装载时即修复/标注。

---

## 3. 通用“上下文自动连线”策略（*Strategy* 模式）

### 3.1 策略接口（示意命名）

* `RoutingStrategy.connect(aId, bId)`：连线（`a.nextId = b` 或在网关上下文处理）
* `RoutingStrategy.disconnect(aId, bId)`：断开（清空/替换对应 `nextId` 或分支 `flowIds`）
* `RoutingStrategy.replace(oldId, newId)`：替换节点并保留上下文
* `RoutingStrategy.insertBetween(prevId, newId, nextId)`：插入
* `RoutingStrategy.extract(targetId)`：抽出目标并自动封口

### 3.2 策略实现

* **线性链策略**：对 `IRoutable` 节点的直接 `nextId` 操作。
* **网关入口/出口策略**：

    * 入口：在网关**前**插入/删除时，需要维护网关节点的前驱 `prevId`（若有）与网关后的汇集关系（若采用显式汇合节点）。
    * 分支尾部：`nextId==""` 默认表示等待汇合；若插入普通节点，连至该节点；若“左侧整体放置”，连至左分支链表头并将右分支指向结束。
* **子流程策略**：将子流程视为“黑盒节点”，其外侧仍按线性策略处理；内部编辑需切换 `scope`。

### 3.3 冲突消解与规则

* 若上下文有**已有 `nextId=99`** 且本次操作又要创建“结束连线”，需先通过“结束唯一性校验器”（见 §7.1）审批；
* 在网关中插入节点，优先判断是“**作为分支公共前置/后置**”还是“**某一分支的内部**”，依 `EditContext.position` 决定。

---

## 4. 网关操作

### 4.1 新增网关（默认并行）

* **设计模式**：

    * *Factory*：`GatewayFactory.create(GatewayType.PARALLEL)`
    * *Strategy*：落位策略 `PlaceOnLeft` / `PlaceAsIs`
* **步骤**（通用前置：校验上下文位置合法）：

    1. 在 `Repository` 中生成网关 ID 与**两条默认分支头**（分支节点）ID；
    2. 断开 `prevId -> nextId`，改为 `prevId -> gateway`；
    3. `gateway.flowIds = [branchL, branchR]`（顺序可配置）；
    4. **落位策略**：

        * **左侧整体放置**：将“网关下方的所有节点”串接至**左分支**（即把原 `gateway` 之后的链全搬到左分支），而**左右两条默认分支的尾部都 `nextId=""`**；右分支暂为空链或默认占位节点。
        * **不移动**：仅插入网关，**当前节点后的链保持不动**；两条默认分支尾部 `nextId=""` 等待汇集，最终上下文在网关后侧继续。
* **副作用**：

    * 若所在流程已有 `nextId=99`，且落位策略会再造一个“结束连线”，须先通过 §7.1 以避免多 99。
    * 更新受影响节点的 `prevId`（若模型中有）以保持双向一致。

> 样例佐证：当前流程中存在 `gatewayType=2` 的网关与 `flowIds` 两分支，可据此模拟插入位置与落位后的分支形态。

### 4.2 删除网关

* **一般流程**：

    1. 若**分支数 > 2**，先提示简化（保留 2 条或按需求）；
    2. 若 **只剩一条分支**（或删除后只剩一条）：**彻底删除网关与条件分支**，并将**这条分支的末尾**自动连回网关的外部上下文（见 §3 策略）；
    3. 若仍有 ≥2 条分支：执行**安全合并**（需有显式汇合节点或由编辑器逻辑在网关外侧再接**下方节点**）。
* **边界规则（你给定的 3.1.2）**：

    * “删除网关分支操作且只剩一条分支的情况下”，自动坍缩为普通链——即**删除网关**与**条件分支**，**保留最后一条分支的节点**并与上下文连接。

### 4.3 修改网关类型

* **并行 → 唯一**：

    * 分支必须具备**条件**（`IHasConditions`），若缺失则为每条分支生成默认条件/占位；
    * 清理并行计数/同步语义（若有）。
* **唯一 → 并行**：

    * 可移除条件或保留为“文档性条件”；
    * 注意分支尾部 `nextId` 的一致性（并行下通常 `nextId=""`，等待汇合）。

---

## 5. 分支操作

### 5.1 增加分支

* 在 `gateway.flowIds` 末尾插入新分支 ID；
* 对 **唯一分支** 网关：必须补充条件或标注待配置状态。
* 对 **并行分支**：可直接生效，尾部 `nextId=""` 等待汇合。

### 5.2 删除分支

* 移除 `flowIds[i]` 所在分支链；
* 若删除后**仅剩一条分支**：触发 §4.2 的**网关坍缩**逻辑；
* 清理分支内部的 `nextId` 指向，避免悬挂。

### 5.3 调整分支顺序

* 对 `flowIds` 重排；
* 对 **唯一分支**：可选“短路优先”规则（命中顺序即优先）。

### 5.4 复制分支

* 深拷贝分支链（含条件/内部节点），重写所有节点 ID；
* 若复制到唯一分支网关：复制条件或设为“待编辑”。

---

## 6. 普通节点操作

### 6.1 新增

* **线性链**：在 `prevId` 与 `nextId` 之间 `insertBetween`；
* **分支内部**：在 `INSIDE_BRANCH(i)` 按链式插入；
* **子流程外侧**：将子流程视为黑盒节点，插前/插后均按线性策略。

### 6.2 删除

* **extract(targetId)** 并将其前驱与后继自动相连；
* 如目标在**分支头**：相当于删除整条分支或将下一节点提升为分支头（按选项）。

### 6.3 修改

* 属性变更不影响拓扑；
* **替换节点类型**（如审批→抄送）：走 `replace(old,new)`，保持 `prev/next` 不变；
* 若替换为 `IRoutable`→非 `IRoutable`（或反之），需提醒不兼容并拒绝。

---

## 7. 约束校验与健壮性

> 以下校验与默认方法可利用你提供的接口中已有的 `default` 校验（如 `hasValidRoute/Branches/Conditions/SubProcess`）扩展实现。

### 7.1 结束节点唯一性校验

* **规则**：一个 `ProcessNode`（主或子）中，**最多一个** `nextId=99` 的链路（允许不存在）。样例主流程与子流程各有一处 `nextId=99`，符合“各自唯一”的要求。
* **实现**：

    * `EndSentinelEnforcer.scan(processNode)`：统计 `nextId==99` 的数量；
    * 如在 `nextId=99` 的节点上插入/修改/删除操作，需重新扫描；
    * `nextId=99` 之后进行插入操作，自动把插入节点的 `nextId="99"`, 但是在网关中，始终保持最上级网关为 `nextId=99`；
    * 在 *Unit of Work* 提交前统一校验，不通过则拒绝变更并返回冲突位点。

### 7.2 路由合法性（`IRoutable.hasValidRoute()`）

* `nextId` 允许空串（分支尾部）或“99”；非空非“99”时，必须在 `flowNodeMap` 中存在。

### 7.3 分支合法性（`IHasBranches.hasValidBranches()`）

* `flowIds` 非空且所有 ID 存在、可达；
* 对 **唯一分支**，要求每条分支具备有效条件（或待配置标志）。

### 7.4 条件合法性（`IHasConditions.hasValidConditions()`）

* 每个条件组元素具备 `nodeId/filedId` 等必要属性；
* 提供条件引用检查（来源字段/节点是否存在）。

### 7.5 子流程合法性（`IHasSubProcess.hasValidSubProcess()`）

* 子流程 `id/startEventId/flowNodeMap` 完整；
* 在样例中，外层节点挂载了一个 `processNode`，其内部 `startEventId` 与 `flowNodeMap` 完整`。

### 7.6 整体一致性扫描

* **前驱/后继一致**（若模型维护 `prveId` 则同步修复）；
* **孤立节点**（在 `flowNodeMap` 存在但不可达）；
* **汇合合理性**（分支尾部 `nextId=""` 时，下方链路不应被误触发）。
* **跨作用域检查**：主流程与子流程的 `99` 互不影响，但各自独立校验。

---

## 8. 事务化编辑(`Unit of Work`（UoW）)

* 每次编辑创建 UoW，收集 **拓扑变更** 与 **属性变更**；
* 如果收集到多个，按队列顺序执行，若中途失败则**恢复到初始状态**（不实现撤销/重做）；
* 提交前执行 §7 的**统一校验**；失败则**拒绝提交**（不实现撤销/重做）。
---

## 10. 基于当前 `workFlow.txt` 的示例走查

> 以下仅用于验证方案与样例相符，关键字段均来自 `workFlow.txt`。

1. **识别网关与分支**：

    * 网关节点 `typeId=1`，`gatewayType=2`（唯一分支），`flowIds` 含两条分支链头；分支 `typeId=2`。
2. **分支尾部形态**：

    * 一条分支尾部 `nextId=""`（等待汇合）；另一条分支或后续普通节点可能指向具体节点或结束。
3. **结束节点哨兵**：

    * 主流程某普通节点 `nextId=99`（如“通知下级部门”）；子流程内部审批节点也有 `nextId=99`，体现“各流程内唯一、不同流程可各自存在”的规则。
4. **子流程**：

    * 外层节点（`typeId=26`）包含 `processNode`，其内部 `startEventId` 与 `flowNodeMap` 完整，可被遍历器递归进入，再按本方案执行 CRUD。

---

## 关键用例的落地步骤（无代码）

### A. 新增网关（默认并行）——“左侧整体放置”

1. **输入**：`EditContext`（在某普通节点之后）、`GatewayType.PARALLEL`、`PlaceOnLeft`。
2. **断连**：`prev -> next` 改为 `prev -> gateway`。
3. **建构**：创建网关与两条默认分支头（网关 `nextId=99`, 分支尾 `nextId=""`）。
4. **搬迁**：将原“网关下方所有节点链”整体搬到**左分支**（修改左分支头的 `nextId` 指向原链头）。
5. **校验**：

    * 结束唯一性（若原链已有 `99`，且右分支也指向 `99`，冲突则拒绝或改为 `""`）。
    * 分支合法性、路由合法性。
6. **提交**：通过 UoW 校验后生效。

### B. 新增网关（默认并行）——“不移动”

1. **输入**：`EditContext`、`PlaceAsIs`。
2. **断连**：`prev -> next` 改为 `prev -> gateway`。
3. **建构**：两分支头尾部 `nextId=""`；
4. **不搬迁**：保持原链在网关外侧，等待将来分支汇合再接下方节点。
5. **校验** → **提交**：同上。

### C. 删除网关（只剩一条分支的情况）

1. **输入**：目标网关 ID；
2. **检查**：`flowIds.size()==1`；
3. **坍缩**：删除网关与分支节点，保留该分支链为普通链表；
4. **重连**：将分支尾与网关外部上下文自动连线（`extract()+connect()`）；
5. **校验**：`99` 唯一、路由一致性；
6. **提交**。

### D. 分支复制

1. **输入**：`gatewayId`、`branchIndex`；
2. **深拷贝**：克隆整条链，重写 ID；复制条件（唯一分支）或置为待配置（并行可选）；
3. **插入**：在 `flowIds` 中追加；
4. **校验** → **提交**。

### E. 普通节点删除

1. **输入**：`nodeId`；
2. **抽出**：`extract(nodeId)`，获得 `prev/next`；
3. **重连**：`connect(prev, next)`；
4. **校验**（含子流程边界）→ **提交**。

---

## 稳定性与健壮性要点清单（落地必做）

* **跨作用域 `99` 唯一**：主/子流程分别校验，样例中二者各有 `99`；编辑操作不得制造“重复 `99`”。
* **空 `nextId` 的语义**：仅允许**分支尾部**使用，其他位置视为错误并拒绝提交。
* **子流程递归**：所有 CRUD 先判定 `scope` 再执行，严禁误把子流程内部节点与外部链接。
* **一致性扫描**：提交前跑一次全图连通性与引用完整性检查，发现游离节点或悬空前驱/后继即拒绝。
* **无撤销/重做**：以 UoW + 快照审计替代（可观测、可追溯）。

---

以上方案以 **遍历 + 策略 + 工厂 + 校验** 为主干，同时满足你列出的网关/分支/普通节点的全量 CRUD 与副作用控制。落地时建议先实现：

1. `Repository`（装载/保存 + 快照）
2. `GraphTraverser` + `NodeVisitor`
3. `RoutingStrategy` 若干实现（线性、网关、子流程）
4. `GatewayCommand`、`BranchCommand`、`NodeCommand`（封装各自的用例步骤）
5. `ValidatorSuite`（结束唯一性、能力合法性、连通性）

完成后即可在样例 `workFlow.txt` 上进行“左侧整体放置/不移动”的网关插入、分支复制/调序、节点增删改等端到端演练。